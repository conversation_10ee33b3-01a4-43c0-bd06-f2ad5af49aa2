
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import UnifiedHeader from "../../components/toolbars/UnifiedHeader";
import BottomNavigationBar from "../../components/toolbars/BottomNavigationBar";
import { Compass, Users, Camera, Cloud, Calendar, Mail, CalendarDays } from "lucide-react";

export function Layout() {
    const navigate = useNavigate();
    const location = useLocation();
    const isBusinessMode = false;
    const isAuthenticated = false;

    // Tab configuration for different sections with icons
    const tabs = [
        { id: "experience", label: "Esperienze", icon: Compass },
        { id: "social", label: "Social", badge: 2, icon: Users },
        { id: "ar", label: "AR Deals", icon: Camera },
        { id: "meteo", label: "Meteo", badge: 1, icon: Cloud },
        { id: "eventi", label: "Eventi", icon: Calendar },
        { id: "email", label: "Email", icon: Mail },
        { id: "calendar", label: "Calendar", icon: CalendarDays }
    ];

    // Determine active tab based on current route
    const getActiveTab = () => {
        if (location.pathname === '/experiences') return 'experience';
        if (location.pathname.startsWith('/social')) return 'social';
        if (location.pathname.startsWith('/ar')) return 'ar';
        if (location.pathname.startsWith('/meteo')) return 'meteo';
        if (location.pathname.startsWith('/eventi')) return 'eventi';
        if (location.pathname.startsWith('/email')) return 'email';
        if (location.pathname.startsWith('/calendar')) return 'calendar';
        return 'experiences'; // default
    };

    const handleTabChange = (tabId: string) => {
        switch (tabId) {
            case 'experience':
                navigate('/experiences');
                break;
            case 'social':
                navigate('/social');
                break;
            case 'ar':
                navigate('/ar');
                break;
            case 'meteo':
                navigate('/meteo');
                break;
            case 'eventi':
                navigate('/eventi');
                break;
            case 'email':
                navigate('/email');
                break;
            case 'calendar':
                navigate('/calendar');
                break;
            default:
                navigate('/experiences');
        }
    };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader 
        variant="with-tabs"
        tabs={tabs}
        activeTab={getActiveTab()}
        onTabChange={handleTabChange}
      />
      <main className="pt-2 pb-24 px-4">
        <Outlet /> {/* Content specific to the route will be rendered here */}
      </main>

      <BottomNavigationBar
        isBusiness={isBusinessMode}
        showVoiceButton={true}
      />
    </div>
  );
}
