import { useState } from 'react';
import { Search, Archive, Star, Send, Paperclip, Trash2, Reply, Forward, MoreHorizontal, Inbox, Mail, Users, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

interface EmailItem {
  id: string;
  sender: string;
  subject: string;
  preview: string;
  time: string;
  isRead: boolean;
  isStarred: boolean;
  isImportant: boolean;
  category: 'booking' | 'deal' | 'business' | 'general';
}

const mockEmails: EmailItem[] = [
  {
    id: '1',
    sender: 'CatchUp Notifications',
    subject: 'Prenotazione confermata - Massaggio rilassante',
    preview: 'La tua prenotazione per il 25 gennaio alle 14:30 è stata confermata.',
    time: '10:30',
    isRead: false,
    isStarred: true,
    isImportant: true,
    category: 'booking'
  },
  {
    id: '2',
    sender: 'Spa Wellness Center',
    subject: 'Promemoria appuntamento',
    preview: 'Ti ricordiamo il tuo appuntamento di domani. Porta con te un documento...',
    time: '09:15',
    isRead: false,
    isStarred: false,
    isImportant: false,
    category: 'booking'
  },
  {
    id: '3',
    sender: 'CatchUp Deals',
    subject: 'Nuove offerte nella tua zona!',
    preview: 'Scopri 5 nuove offerte imperdibili per ristoranti e spa vicino a te.',
    time: 'Ieri',
    isRead: true,
    isStarred: false,
    isImportant: false,
    category: 'deal'
  },
  {
    id: '4',
    sender: 'Ristorante Da Mario',
    subject: 'Grazie per la tua recensione',
    preview: 'Grazie per aver lasciato una recensione positiva. Ti aspettiamo presto!',
    time: 'Ieri',
    isRead: true,
    isStarred: true,
    isImportant: false,
    category: 'business'
  },
  {
    id: '5',
    sender: 'CatchUp Support',
    subject: 'Aggiornamento dell\'app disponibile',
    preview: 'È disponibile la nuova versione dell\'app con miglioramenti e nuove funzionalità.',
    time: '2 giorni fa',
    isRead: true,
    isStarred: false,
    isImportant: false,
    category: 'general'
  }
];

const sidebarItems = [
  { icon: Inbox, label: 'Posta in arrivo', count: 2, active: true },
  { icon: Star, label: 'Speciali', count: 2 },
  { icon: Send, label: 'Inviati' },
  { icon: Archive, label: 'Archivio' },
  { icon: Trash2, label: 'Cestino' },
];

const categories = [
  { id: 'all', label: 'Tutte', color: 'default' },
  { id: 'booking', label: 'Prenotazioni', color: 'blue' },
  { id: 'deal', label: 'Offerte', color: 'green' },
  { id: 'business', label: 'Business', color: 'purple' },
  { id: 'general', label: 'Generali', color: 'gray' }
];

const Email = () => {
  const [selectedEmail, setSelectedEmail] = useState<EmailItem | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredEmails = mockEmails.filter(email => {
    const matchesCategory = selectedCategory === 'all' || email.category === selectedCategory;
    const matchesSearch = email.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         email.sender.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         email.preview.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getCategoryColor = (category: string) => {
    const cat = categories.find(c => c.id === category);
    return cat?.color || 'default';
  };

  const unreadCount = mockEmails.filter(email => !email.isRead).length;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-background border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-foreground">Email</h1>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Search Bar */}
        <div className="mt-4 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Cerca nelle email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filters */}
        <div className="flex gap-2 mt-4 overflow-x-auto pb-2">
          {categories.map((category) => (
            <Badge
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              className="cursor-pointer whitespace-nowrap"
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.label}
            </Badge>
          ))}
        </div>
      </div>

      <div className="flex h-[calc(100vh-200px)]">
        {/* Sidebar */}
        <div className="w-64 bg-muted/30 border-r p-4 hidden md:block">
          <div className="space-y-2">
            {sidebarItems.map((item, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                  item.active 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-muted'
                }`}
              >
                <div className="flex items-center gap-3">
                  <item.icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                </div>
                {item.count && (
                  <Badge variant={item.active ? "secondary" : "default"} className="text-xs">
                    {item.count}
                  </Badge>
                )}
              </div>
            ))}
          </div>

          <Separator className="my-4" />

          {/* Quick Actions */}
          <div className="space-y-2">
            <Button className="w-full justify-start" variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Componi
            </Button>
          </div>
        </div>

        {/* Email List */}
        <div className="flex-1 flex">
          <div className="w-full md:w-96 border-r">
            <div className="p-4 border-b bg-muted/20">
              <div className="flex items-center justify-between">
                <h2 className="font-semibold text-foreground">
                  Posta in arrivo ({unreadCount} non lette)
                </h2>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <ScrollArea className="h-full">
              <div className="divide-y">
                {filteredEmails.map((email) => (
                  <div
                    key={email.id}
                    className={`p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
                      selectedEmail?.id === email.id ? 'bg-muted' : ''
                    } ${!email.isRead ? 'bg-blue-50/50 border-l-2 border-l-primary' : ''}`}
                    onClick={() => setSelectedEmail(email)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className={`font-medium text-sm ${!email.isRead ? 'font-bold' : ''}`}>
                          {email.sender}
                        </span>
                        {email.isStarred && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                        {email.isImportant && (
                          <Badge variant="destructive" className="text-xs px-1">
                            !
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">{email.time}</span>
                    </div>
                    
                    <h3 className={`text-sm mb-1 line-clamp-1 ${!email.isRead ? 'font-semibold' : ''}`}>
                      {email.subject}
                    </h3>
                    
                    <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                      {email.preview}
                    </p>
                    
                    <Badge 
                      variant="outline" 
                      className="text-xs"
                    >
                      {categories.find(c => c.id === email.category)?.label}
                    </Badge>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Email Content */}
          <div className="flex-1 hidden md:block">
            {selectedEmail ? (
              <div className="h-full flex flex-col">
                {/* Email Header */}
                <div className="p-6 border-b bg-muted/20">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold mb-2">{selectedEmail.subject}</h2>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span><strong>Da:</strong> {selectedEmail.sender}</span>
                        <span><strong>Ora:</strong> {selectedEmail.time}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Star className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Archive className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Reply className="h-4 w-4 mr-2" />
                      Rispondi
                    </Button>
                    <Button variant="outline" size="sm">
                      <Forward className="h-4 w-4 mr-2" />
                      Inoltra
                    </Button>
                  </div>
                </div>

                {/* Email Body */}
                <div className="flex-1 p-6">
                  <div className="prose max-w-none">
                    <p>Caro utente,</p>
                    <p>{selectedEmail.preview}</p>
                    
                    {selectedEmail.category === 'booking' && (
                      <div className="mt-6 p-4 bg-blue-50 rounded-lg border">
                        <h4 className="font-semibold text-blue-900 mb-2">Dettagli Prenotazione</h4>
                        <div className="text-sm text-blue-800 space-y-1">
                          <p><strong>Servizio:</strong> Massaggio rilassante</p>
                          <p><strong>Data:</strong> 25 gennaio 2025</p>
                          <p><strong>Orario:</strong> 14:30 - 15:30</p>
                          <p><strong>Location:</strong> Spa Wellness Center</p>
                        </div>
                      </div>
                    )}
                    
                    {selectedEmail.category === 'deal' && (
                      <div className="mt-6 p-4 bg-green-50 rounded-lg border">
                        <h4 className="font-semibold text-green-900 mb-2">Offerte Disponibili</h4>
                        <div className="text-sm text-green-800">
                          <p>Scopri le migliori offerte nella tua zona con sconti fino al 50%!</p>
                          <Button variant="outline" size="sm" className="mt-2">
                            Visualizza Offerte
                          </Button>
                        </div>
                      </div>
                    )}
                    
                    <p className="mt-6">
                      Cordiali saluti,<br />
                      Il team CatchUp
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-muted-foreground">
                    Seleziona un'email per visualizzarne il contenuto
                  </h3>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Email View */}
      {selectedEmail && (
        <div className="md:hidden fixed inset-0 bg-background z-50">
          <div className="h-full flex flex-col">
            <div className="p-4 border-b bg-muted/20">
              <div className="flex items-center justify-between mb-4">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setSelectedEmail(null)}
                >
                  ← Torna alla lista
                </Button>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm">
                    <Star className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <h2 className="text-lg font-semibold mb-2">{selectedEmail.subject}</h2>
              <div className="text-sm text-muted-foreground">
                <span><strong>Da:</strong> {selectedEmail.sender}</span>
                <span className="ml-4"><strong>Ora:</strong> {selectedEmail.time}</span>
              </div>
            </div>

            <div className="flex-1 p-4 overflow-y-auto">
              <div className="prose max-w-none">
                <p>{selectedEmail.preview}</p>
                <p className="mt-4">
                  Cordiali saluti,<br />
                  Il team CatchUp
                </p>
              </div>
            </div>

            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Button className="flex-1">
                  <Reply className="h-4 w-4 mr-2" />
                  Rispondi
                </Button>
                <Button variant="outline">
                  <Forward className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Email;