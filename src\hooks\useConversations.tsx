import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Conversation, Message } from "@/types/conversations";
import { useAuth } from "@/hooks/auth/useAuth";
import { J<PERSON> } from "@/integrations/supabase/types";

export const useConversations = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchConversations = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    try {
      const { data: allConversationsData, error } = await supabase
        .from('conversations_with_details')
        .select('*')
        .order('last_message_at', { ascending: false });

      if (error) throw error;

      // Convertiamo i dati al tipo Conversation e filtriamo solo le conversazioni in cui l'utente è partecipante
      if (allConversationsData) {
        const typedConversations = allConversationsData
          .map(conv => {
            return {
              id: conv.id,
              business_id: conv.business_id,
              deal_id: conv.deal_id,
              booking_id: conv.booking_id,
              created_at: conv.created_at,
              updated_at: conv.updated_at,
              last_message_at: conv.last_message_at,
              type: conv.type,
              created_by: conv.owner_id || '', // Usiamo owner_id come created_by se disponibile
              is_active: true, // Impostiamo true come valore predefinito
              
              // Campi aggiuntivi dalla vista
              business_name: conv.business_name,
              business_photo: conv.business_photo,
              deal_title: conv.deal_title,
              deal_image: conv.deal_image,
              participants: conv.participants,
              last_message: conv.last_message,
              unread_count: conv.unread_count,
              is_booking: conv.booking_id ? true : false // Determiniamo se è una prenotazione
            } as Conversation;
          })
          .filter(conv => {
            // Verifichiamo se l'utente corrente è tra i partecipanti
            if (!conv.participants || !Array.isArray(conv.participants)) {
              return false;
            }
            return conv.participants.some(participant => participant.user_id === user.id);
          });
        
        // Ora mostriamo tutte le conversazioni nella tab "Conversazioni"
        setConversations(typedConversations);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast.error("Errore nel caricamento delle conversazioni");
    } finally {
      setIsLoading(false);
    }
  };

  async function saveMessage(
    {
      conversationId,
      senderId,
      content,
      metadata = {}
    }: {
      conversationId: string,
      senderId: string,
      content: string,
      metadata?: Json
    }
  ) {
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          user_id: senderId,
          content,
          metadata: metadata
        });

      if (error) throw error;

      // toast.success("Messaggio inviato");
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error("Errore nell'invio del messaggio");
    }
  }

  const markAsRead = async (messageId: string) => {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ read_at: new Date().toISOString() })
        .eq('id', messageId);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  // Imposta il listener per gli aggiornamenti in tempo reale
  useEffect(() => {
    const channel = supabase
      .channel('chat-updates')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          console.log('Nuovo messaggio:', payload);
          fetchConversations();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Carica le conversazioni all'avvio
  useEffect(() => {
    if (user) {
      fetchConversations();
    }
  }, [user]);

  return {
    conversations,
    isLoading,
    saveMessage,
    markAsRead,
    refreshConversations: fetchConversations
  };
};
