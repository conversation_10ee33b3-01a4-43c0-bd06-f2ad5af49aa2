import { useState, useEffect } from "react";
import { Search, X, Filter, MapPin, Mic, ChevronDown, ChevronUp } from "lucide-react";
import { motion, AnimatePresence, PanInfo } from "framer-motion";
import Categories from "@/components/category/Categories";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { useCategoryQuery } from "@/queries/useCategoryQuery";
import { NEARBY_DEFAULT_MAX_RADIUS_IN_KM, NEARBY_DEFAULT_MIN_RADIUS_IN_KM, NEARBY_DEFAULT_RADIUS_IN_KM } from "@/data/userSettings";
import type { FilterSettings } from "@/stores/useFilterStore";

export type SearchFilters = FilterSettings;

interface ModernSearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onDistanceChange: (distance: number) => void;
  placeholder?: string;
  showLocationFilter?: boolean;
  showBusinessFilters?: boolean;
  showSortOptions?: boolean;
  showTextSearch?: boolean;
}

const ModernSearchPanel = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onDistanceChange,
  placeholder = "Cerca offerte e attività",
  showLocationFilter = false,
  showBusinessFilters = true,
  showSortOptions = false,
  showTextSearch = false,
}: ModernSearchPanelProps) => {
  const { data: categoriesData, isLoading: isLoadingCategories } = useCategoryQuery();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, searchQuery: value });
  };

  const handleCategorySelect = (categoryId: string) => {
    onFiltersChange({ 
      ...filters, 
      selectedCategoryIds: filters.selectedCategoryIds.includes(categoryId) 
        ? filters.selectedCategoryIds.filter(id => id !== categoryId)
        : [...filters.selectedCategoryIds, categoryId]
    });
  };

  const handleBusinessFilterChange = (key: 'withDeals' | 'withBookings') => {
    onFiltersChange({ ...filters, [key]: !filters[key] });
  };

  const handleRadiusChange = (value: number[]) => {
    onFiltersChange({ ...filters, radius: value[0] });
    onDistanceChange(value[0]);
  };

  const handleSortChange = (sortBy: SearchFilters['sortBy']) => {
    onFiltersChange({ ...filters, sortBy });
  };

  const clearFilters = () => {
    onFiltersChange({
      searchQuery: '',
      selectedCategoryIds: [],
      withDeals: false,
      withBookings: false,
      radius: NEARBY_DEFAULT_RADIUS_IN_KM,
      sortBy: 'relevance',
      applicationAccess: filters.applicationAccess,
    });
  };

  const removeFilter = (filterType: string, value?: string) => {
    switch (filterType) {
      case 'search':
        onFiltersChange({ ...filters, searchQuery: '' });
        break;
      case 'category':
        if (value) {
          onFiltersChange({ 
            ...filters, 
            selectedCategoryIds: filters.selectedCategoryIds.filter(id => id !== value)
          });
        }
        break;
      case 'withDeals':
        onFiltersChange({ ...filters, withDeals: false });
        break;
      case 'withBookings':
        onFiltersChange({ ...filters, withBookings: false });
        break;
      case 'radius':
        onFiltersChange({ ...filters, radius: NEARBY_DEFAULT_RADIUS_IN_KM });
        onDistanceChange(NEARBY_DEFAULT_RADIUS_IN_KM);
        break;
    }
  };

  const getActiveFilters = () => {
    const active = [];
    if (filters.searchQuery) active.push({ type: 'search', label: filters.searchQuery, value: filters.searchQuery });
    if (filters.selectedCategoryIds.length > 0) {
      filters.selectedCategoryIds.forEach(id => {
        const category = categoriesData?.find(c => c.id === id);
        if (category) active.push({ type: 'category', label: category.name, value: id });
      });
    }
    if (filters.withDeals) active.push({ type: 'withDeals', label: 'Con offerte' });
    if (filters.withBookings) active.push({ type: 'withBookings', label: 'Con prenotazioni' });
    if (filters.radius !== NEARBY_DEFAULT_RADIUS_IN_KM) {
      active.push({ type: 'radius', label: `${filters.radius}km` });
    }
    return active;
  };

  const activeFilters = getActiveFilters();

  const handleDragEnd = (event: any, info: PanInfo) => {
    if (info.offset.y > 100) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40"
          />

          {/* Bottom Sheet */}
          <motion.div
            initial={{ y: "100%" }}
            animate={{ y: isExpanded ? "10%" : "60%" }}
            exit={{ y: "100%" }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={0.1}
            onDragEnd={handleDragEnd}
            className="fixed inset-x-0 bottom-0 z-50 bg-background/95 backdrop-blur-xl border-t border-border rounded-t-3xl shadow-2xl max-h-[90vh] overflow-hidden"
          >
            {/* Pull Handle with Visual Hint */}
            <div className="flex flex-col items-center py-3 cursor-grab active:cursor-grabbing">
              <div className="w-10 h-1.5 bg-muted-foreground/30 rounded-full mb-1" />
              <motion.p 
                className="text-xs text-muted-foreground/60 font-medium"
                initial={{ opacity: 1 }}
                animate={{ opacity: [1, 0.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Trascina per chiudere • Tocca per espandere
              </motion.p>
            </div>

            {/* Header with Clear Instructions */}
            <div className="px-6 pb-4">
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-xl font-semibold text-foreground">Filtri di ricerca</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-muted-foreground hover:text-foreground rounded-full h-10 w-10 p-0"
                >
                  {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronUp className="h-5 w-5" />}
                </Button>
              </div>
              
              {/* Quick Access Instruction */}
              {!isExpanded && (
                <motion.p 
                  className="text-xs text-muted-foreground mb-3 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  Tocca le categorie per filtrare • Premi ↑ per più opzioni
                </motion.p>
              )}
              
              {isExpanded && (
                <motion.p 
                  className="text-sm text-muted-foreground mb-3"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  Applica i filtri per trovare esattamente quello che cerchi
                </motion.p>
              )}

              {/* Floating Search Bar */}
              {showTextSearch && (
                <motion.div 
                  className="relative mb-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <div className="relative bg-muted/50 backdrop-blur-sm rounded-2xl border border-border/50 shadow-lg">
                    <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5" />
                    <input
                      type="text"
                      placeholder={placeholder}
                      className="w-full px-12 py-4 bg-transparent rounded-2xl text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:bg-background/80 transition-all duration-300"
                      value={filters.searchQuery}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      autoFocus={false}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 rounded-full hover:bg-primary/10"
                    >
                      <Mic className="h-4 w-4 text-primary" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Active Filters Chips with Instruction */}
              {activeFilters.length > 0 ? (
                <motion.div 
                  className="mb-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <p className="text-xs text-muted-foreground mb-2 font-medium">
                    Filtri attivi • Tocca per rimuovere
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {activeFilters.map((filter, index) => (
                      <Badge
                        key={`${filter.type}-${filter.value || index}`}
                        variant="secondary"
                        className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors cursor-pointer flex items-center gap-1 px-3 py-1.5 rounded-full"
                        onClick={() => removeFilter(filter.type, filter.value)}
                      >
                        {filter.label}
                        <X className="h-3 w-3 ml-1" />
                      </Badge>
                    ))}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs text-muted-foreground hover:text-foreground h-7 px-2"
                    >
                      Cancella tutto
                    </Button>
                  </div>
                </motion.div>
              ) : (
                <motion.div 
                  className="mb-4 text-center py-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <p className="text-sm text-muted-foreground">
                    Nessun filtro applicato • Inizia selezionando una categoria
                  </p>
                </motion.div>
              )}
            </div>

            {/* Scrollable Content */}
            <div className="overflow-y-auto max-h-[60vh] px-6 pb-6">
              {/* Quick Categories */}
              <motion.div 
                className="mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                    Categorie
                  </h3>
                  <span className="text-xs text-muted-foreground/60">
                    Tocca per selezionare
                  </span>
                </div>
                {isLoadingCategories ? (
                  <div className="flex space-x-3 overflow-x-auto pb-2">
                    {[1, 2, 3, 4].map((item) => (
                      <div key={item} className="flex-shrink-0">
                        <div className="w-16 h-16 bg-muted rounded-2xl animate-pulse" />
                        <div className="w-12 h-3 bg-muted rounded mt-2 animate-pulse mx-auto" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <Categories
                    categories={categoriesData}
                    selectedCategoryIds={filters.selectedCategoryIds}
                    onCategorySelect={handleCategorySelect}
                  />
                )}
              </motion.div>

              {/* Advanced Filters Toggle */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  variant="ghost"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="w-full justify-between text-muted-foreground hover:text-foreground mb-4 h-12 rounded-xl border border-border/30 hover:border-border/60 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    <span className="font-medium">Filtri avanzati</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <span className="text-muted-foreground/60">
                      {showAdvancedFilters ? 'Nascondi' : 'Mostra'}
                    </span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
                  </div>
                </Button>

                <AnimatePresence>
                  {showAdvancedFilters && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6"
                    >
                      {/* Location Filter */}
                      {showLocationFilter && (
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-primary" />
                            <h4 className="font-medium text-foreground">Distanza</h4>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between text-sm text-muted-foreground">
                              <span>Raggio di ricerca</span>
                              <span className="font-medium text-primary">{filters.radius} km</span>
                            </div>
                            <Slider
                              value={[filters.radius]}
                              onValueChange={handleRadiusChange}
                              max={NEARBY_DEFAULT_MAX_RADIUS_IN_KM}
                              min={NEARBY_DEFAULT_MIN_RADIUS_IN_KM}
                              step={0.1}
                              className="w-full"
                            />
                          </div>
                        </div>
                      )}

                      {/* Business Filters */}
                      {showBusinessFilters && (
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <Filter className="h-4 w-4 text-primary" />
                            <h4 className="font-medium text-foreground">Tipo di attività</h4>
                          </div>
                          <div className="grid grid-cols-1 gap-3">
                            <label className="flex items-center justify-between p-4 bg-muted/30 rounded-xl cursor-pointer hover:bg-muted/50 transition-colors border border-border/50">
                              <span className="font-medium text-foreground">Con offerte speciali</span>
                              <input
                                type="checkbox"
                                checked={filters.withDeals}
                                onChange={() => handleBusinessFilterChange('withDeals')}
                                className="w-5 h-5 rounded border-2 border-primary text-primary focus:ring-primary/20 bg-transparent"
                              />
                            </label>
                            <label className="flex items-center justify-between p-4 bg-muted/30 rounded-xl cursor-pointer hover:bg-muted/50 transition-colors border border-border/50">
                              <span className="font-medium text-foreground">Prenotazioni disponibili</span>
                              <input
                                type="checkbox"
                                checked={filters.withBookings}
                                onChange={() => handleBusinessFilterChange('withBookings')}
                                className="w-5 h-5 rounded border-2 border-primary text-primary focus:ring-primary/20 bg-transparent"
                              />
                            </label>
                          </div>
                        </div>
                      )}

                      {/* Sort Options */}
                      {showSortOptions && (
                        <div className="space-y-4">
                          <h4 className="font-medium text-foreground">Ordina per</h4>
                          <div className="grid grid-cols-2 gap-3">
                            {[
                              { value: 'relevance', label: 'Rilevanza' },
                              { value: 'distance', label: 'Distanza' },
                              { value: 'rating', label: 'Valutazione' },
                              { value: 'price', label: 'Prezzo' },
                            ].map((option) => (
                              <button
                                key={option.value}
                                onClick={() => handleSortChange(option.value as SearchFilters['sortBy'])}
                                className={`p-4 rounded-xl font-medium transition-all duration-200 border ${
                                  filters.sortBy === option.value
                                    ? 'bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/20'
                                    : 'bg-muted/30 text-foreground border-border/50 hover:bg-muted/50'
                                }`}
                              >
                                {option.label}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ModernSearchPanel;